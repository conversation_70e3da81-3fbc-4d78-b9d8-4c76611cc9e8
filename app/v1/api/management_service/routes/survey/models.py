"""
Survey models for survey questions and submissions management.
Simplified to match task-item question format.
"""

from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from enum import Enum
from bson import ObjectId

from app.shared.object_id import PyObjectId, object_id_field, validate_object_id


class SurveyQuestionType(str, Enum):
    """Enum for survey question types."""
    SINGLE_CHOICE = "single_choice"
    MULTIPLE_CHOICE = "multiple_choice"
    TEXT = "text"
    RATING = "rating"
    YES_NO = "yes_no"
    SCALE = "scale"


class SurveyQuestionStatus(str, Enum):
    """Enum for survey question status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"


class SurveySubmissionStatus(str, Enum):
    """Enum for survey submission status."""
    COMPLETED = "completed"
    PARTIAL = "partial"
    ABANDONED = "abandoned"


class SurveyQuestion(BaseModel):
    """Model for survey questions - simplified format like task-item questions."""
    id: PyObjectId = object_id_field()
    text: str = Field(..., description="Question text")
    question_type: SurveyQuestionType = Field(..., description="Type of question")
    options: Optional[Dict[str, str]] = Field(None, description="Question options (a: option1, b: option2)")
    status: SurveyQuestionStatus = Field(default=SurveyQuestionStatus.ACTIVE, description="Question status")
    category: Optional[str] = Field(None, description="Question category")

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    created_by: Optional[PyObjectId] = Field(None, description="User ID who created the question")
    updated_by: Optional[PyObjectId] = Field(None, description="User ID who last updated the question")

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_encoders={ObjectId: str}
    )

    @field_validator("id", mode="before")
    @classmethod
    def validate_object_id(cls, v):
        return validate_object_id(v)


class SurveyAnswer(BaseModel):
    """Model for individual survey answers."""
    question_id: str = Field(..., description="Survey question ID")
    answer_value: Union[str, int, float, List[str]] = Field(..., description="Answer value")
    answer_text: Optional[str] = Field(None, description="Free text answer for text questions")
    answered_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))

    model_config = ConfigDict(
        arbitrary_types_allowed=True
    )


class SurveySubmission(BaseModel):
    """Model for survey submissions."""
    id: PyObjectId = object_id_field()
    user_id: PyObjectId = Field(..., description="User ID who submitted the survey")
    answers: List[SurveyAnswer] = Field(..., description="List of survey answers")
    status: SurveySubmissionStatus = Field(default=SurveySubmissionStatus.COMPLETED, description="Submission status")
    
    # Timestamps
    started_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    submitted_at: Optional[datetime] = Field(None, description="When the survey was submitted")
    last_updated_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    ip_address: Optional[str] = Field(None, description="IP address of the submitter")
    user_agent: Optional[str] = Field(None, description="User agent of the submitter")

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_encoders={ObjectId: str}
    )

    @field_validator("id", mode="before")
    @classmethod
    def validate_object_id(cls, v):
        return validate_object_id(v)


# Request/Response Models

# Request/Response Models

class SurveyQuestionCreate(BaseModel):
    """Model for creating a new survey question."""
    text: str = Field(..., description="Question text")
    options: List[str] = Field(..., description="List of answer options")
    question_type: SurveyQuestionType = Field(default=SurveyQuestionType.SINGLE_CHOICE, description="Type of question")
    status: SurveyQuestionStatus = Field(default=SurveyQuestionStatus.ACTIVE, description="Question status")
    category: Optional[str] = Field(None, description="Question category")


class BulkSurveyQuestionCreate(BaseModel):
    """Model for creating multiple survey questions at once."""
    questions: List[SurveyQuestionCreate] = Field(..., description="List of questions to create")


class SurveyQuestionUpdate(BaseModel):
    """Model for updating a survey question."""
    text: Optional[str] = Field(None, description="Question text")
    options: Optional[List[str]] = Field(None, description="List of answer options")
    question_type: Optional[SurveyQuestionType] = Field(None, description="Type of question")
    status: Optional[SurveyQuestionStatus] = Field(None, description="Question status")
    category: Optional[str] = Field(None, description="Question category")


class SurveyQuestionResponse(BaseModel):
    """Model for survey question response."""
    id: str = Field(..., description="Question ID")
    text: str = Field(..., description="Question text")
    question_type: SurveyQuestionType = Field(..., description="Type of question")
    options: Optional[Dict[str, str]] = Field(None, description="Question options (a: option1, b: option2)")
    status: SurveyQuestionStatus = Field(..., description="Question status")
    category: Optional[str] = Field(None, description="Question category")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    created_by: Optional[str] = Field(None, description="User ID who created the question")
    updated_by: Optional[str] = Field(None, description="User ID who last updated the question")

    model_config = ConfigDict(
        from_attributes=True
    )


class SurveySubmissionCreate(BaseModel):
    """Model for creating a survey submission."""
    answers: List[SurveyAnswer] = Field(..., description="List of survey answers")
    status: SurveySubmissionStatus = Field(default=SurveySubmissionStatus.COMPLETED, description="Submission status")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


class SurveySubmissionResponse(BaseModel):
    """Model for survey submission response."""
    id: str = Field(..., description="Submission ID")
    user_id: str = Field(..., description="User ID who submitted the survey")
    answers: List[SurveyAnswer] = Field(..., description="List of survey answers")
    status: SurveySubmissionStatus = Field(..., description="Submission status")
    started_at: datetime = Field(..., description="When the survey was started")
    submitted_at: Optional[datetime] = Field(None, description="When the survey was submitted")
    last_updated_at: datetime = Field(..., description="Last update timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    model_config = ConfigDict(
        from_attributes=True
    )



